"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Mail, Building2 } from "lucide-react";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import ManageMail from "@/components/settings/admin/mail-management";
import ManageOrganization from "@/components/settings/admin/organization-management";
import { useFetch } from "@/hooks/useFetchOnMount";

export default function Admin() {
  const [editOpen, setEditOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [orgEditOpen, setOrgEditOpen] = useState(false);
  const [organization, setOrganization] = useState(null);
  const { data: fetchedData, loading, error } = useFetch("/admin?func=email");
  const { isRefreshed, setIsRefreshed } = useAuth();
  const router = useRouter();

  const { org_id } = useAuth();
  // Mock fetch/handle for mail
  const handleMailClick = async () => {
    const mockUser = { id: "1", email: "<EMAIL>" };
    setSelectedUser(mockUser);
    setEditOpen(true);
  };

  const handleMailSubmit = async (user) => {
    const { id, ...payload } = user;
    try {
      // await apiClient.patch(`/user/${id}`, payload);
      toast.success("Mail updated successfully");
      setEditOpen(false);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to update mail:", error);
      toast.error("Failed to update mail");
    }
  };

  // Mock fetch/handle for organization
  const handleOrgClick = async () => {
    setOrganization({ id: org_id, name: '' }); // Name will be fetched in the component
    setOrgEditOpen(true);
  };

  const handleOrgSubmit = async (updatedOrg) => {
    const { id, ...payload } = updatedOrg;
    try {
      await apiClient.patch(`/organization/${id}`, payload);
      toast.success("Organization updated successfully");
      setOrgEditOpen(false);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to update organization:", error);
      toast.error("Failed to update organization");
    }
  };
  if (loading || !fetchedData) {
    return <div className="text-center py-10">Loading email config...</div>;
  }
  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
        <h1 className="text-xl sm:text-xl font-bold sm:mb-3 text-wrap">
          Admin
        </h1>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Manage Mail */}
        <div
          onClick={handleMailClick}
          className="cursor-pointer bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition p-6 flex items-start gap-4 hover:bg-blue-50"
        >
          <div className="bg-blue-100 p-3 rounded-full text-blue-600">
            <Mail className="w-6 h-6" />
          </div>
          <div>
            <h2 className="text-md font-semibold text-gray-700">Manage Mail</h2>
            <p className="text-sm text-gray-500">Add, edit, or remove mail</p>
          </div>
        </div>

        {/* Edit Organization */}
        <div
          onClick={handleOrgClick}
          className="cursor-pointer bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition p-6 flex items-start gap-4 hover:bg-blue-50"
        >
          <div className="bg-blue-100 p-3 rounded-full text-blue-600">
            <Building2 className="w-6 h-6" />
          </div>
          <div>
            <h2 className="text-md font-semibold text-gray-700">
              Edit Organization
            </h2>
            <p className="text-sm text-gray-500">
              Create and edit organization
            </p>
          </div>
        </div>
      </div>

      {/* ManageMail Modal */}
      {editOpen && selectedUser && (
        <ManageMail
          initialData={fetchedData[0].value}
          onSubmit={handleMailSubmit}
          onClose={() => setEditOpen(false)}
        />
      )}

      {/* ManageOrganization Modal */}
      {orgEditOpen && organization && (
        <ManageOrganization
          organization={organization}
          onSubmit={handleOrgSubmit}
          onClose={() => setOrgEditOpen(false)}
        />
      )}
    </div>
  );
}
