"use client";
import React, { useState, useEffect, useMemo } from "react";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";
import { Button } from "../ui/button";
import { ChevronLeft, ChevronRight, Sessions, Users, Network, Activity, Router } from "@/components/icons/list";
import { useSearchParams, useRouter } from "next/navigation";
import { DepartmentStats, PackageInfo, NAS } from "@/types/interface-type";
import {
  BarChart,
  LineChart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  RadialBarChart,
  RadialBar,
  Scatter<PERSON>hart,
  <PERSON>atter,
  Treemap,
} from "recharts";

// Enhanced interfaces for new analytics
interface OverviewStats {
  totalActiveUsers: number;
  totalStaffUsers: number;
  totalGuestUsers: number;
  totalSessions: number;
  totalBandwidthGB: number;
  avgSessionDuration: number;
  peakHourUsage: string;
  departmentCount: number;
}

interface UserDetail extends DepartmentStats {
  sessionCount: number;
  avgSessionDuration: number;
  totalBandwidthMB: number;
  firstSeen: string;
  lastSeen: string;
  isStaff: boolean;
  department: string;
}

interface DepartmentAnalytics {
  departmentName: string;
  staffCount: number;
  guestCount: number;
  totalUsers: number;
  staffBandwidth: number;
  guestBandwidth: number;
  totalBandwidth: number;
  avgSessionDuration: number;
  peakUsageHour: string;
}

interface NASAnalytics {
  nasName: string;
  nasIP: string;
  totalSessions: number;
  activeSessions: number;
  totalBandwidthGB: number;
  avgSessionDuration: number;
  vendor: string;
  description: string;
}

interface TimeSeriesData {
  timestamp: string;
  activeUsers: number;
  bandwidthMB: number;
  sessions: number;
  hour?: number;
  dayOfWeek?: string;
  staffUsers?: number;
  guestUsers?: number;
}

interface HourlyUsageData {
  hour: number;
  hourLabel: string;
  usage: number;
  users: number;
  sessions: number;
}

interface DepartmentPieData {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

interface UserTrendData {
  date: string;
  newUsers: number;
  returningUsers: number;
  totalActiveUsers: number;
}

interface SessionDurationData {
  range: string;
  count: number;
  percentage: number;
}

interface DeviceTypeData {
  deviceType: string;
  count: number;
  bandwidth: number;
}

interface TopUsersData {
  username: string;
  usage: number;
  sessions: number;
  avgDuration: number;
  isStaff: boolean;
}

const COLORS = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4", "#84cc16", "#f97316", "#ec4899", "#14b8a6", "#f97316", "#6366f1"];
const DEPARTMENT_COLORS = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4", "#84cc16", "#f97316", "#ec4899", "#14b8a6"];

// Utility functions
const getDefaultDates = () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 7); // Last 7 days
  const today = new Date();
  return {
    start: yesterday.toISOString().split("T")[0],
    end: today.toISOString().split("T")[0]
  };
};

const formatBytes = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${hours}h ${minutes}m`;
};

// Enhanced Overview Chart Component
const OverviewChart: React.FC<{ data: TimeSeriesData[] }> = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <AreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <defs>
          <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
            <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
          </linearGradient>
          <linearGradient id="colorBandwidth" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
            <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
          </linearGradient>
        </defs>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis 
          dataKey="timestamp" 
          tickFormatter={(value) => new Date(value).toLocaleDateString()} 
        />
        <YAxis yAxisId="left" />
        <YAxis yAxisId="right" orientation="right" />
        <Tooltip 
          labelFormatter={(value) => new Date(value).toLocaleString()}
          formatter={(value: any, name: string) => [
            name === 'activeUsers' ? value : `${value} MB`,
            name === 'activeUsers' ? 'Active Users' : 'Bandwidth'
          ]}
        />
        <Legend />
        <Area
          yAxisId="left"
          type="monotone"
          dataKey="activeUsers"
          stroke="#3b82f6"
          fillOpacity={1}
          fill="url(#colorUsers)"
          name="Active Users"
        />
        <Area
          yAxisId="right"
          type="monotone"
          dataKey="bandwidthMB"
          stroke="#10b981"
          fillOpacity={1}
          fill="url(#colorBandwidth)"
          name="Bandwidth (MB)"
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};

// Department Performance Chart
const DepartmentPerformanceChart: React.FC<{ data: DepartmentAnalytics[] }> = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height={400}>
      <BarChart data={data.slice(0, 10)} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="departmentName" angle={-45} textAnchor="end" height={80} />
        <YAxis />
        <Tooltip 
          formatter={(value: any, name: string) => [
            name.includes('Bandwidth') ? `${value} GB` : value,
            name
          ]}
        />
        <Legend />
        <Bar dataKey="staffCount" fill="#3b82f6" name="Staff Users" />
        <Bar dataKey="guestCount" fill="#10b981" name="Guest Users" />
        <Bar dataKey="totalBandwidth" fill="#f59e0b" name="Total Bandwidth (GB)" />
      </BarChart>
    </ResponsiveContainer>
  );
};

// NAS Performance Chart
const NASPerformanceChart: React.FC<{ data: NASAnalytics[] }> = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height={400}>
      <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="nasName" angle={-45} textAnchor="end" height={80} />
        <YAxis />
        <Tooltip 
          formatter={(value: any, name: string) => [
            name.includes('Bandwidth') ? `${value} GB` : value,
            name
          ]}
        />
        <Legend />
        <Bar dataKey="totalSessions" fill="#8b5cf6" name="Total Sessions" />
        <Bar dataKey="activeSessions" fill="#06b6d4" name="Active Sessions" />
        <Bar dataKey="totalBandwidthGB" fill="#f97316" name="Bandwidth (GB)" />
      </BarChart>
    </ResponsiveContainer>
  );
};

// User Type Distribution Chart
const UserTypeDistributionChart: React.FC<{ staffCount: number; guestCount: number }> = ({ staffCount, guestCount }) => {
  const data = [
    { name: 'Staff Users', value: staffCount, color: '#3b82f6' },
    { name: 'Guest Users', value: guestCount, color: '#10b981' },
  ];

  return (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={120}
          paddingAngle={5}
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip formatter={(value: any) => [value, 'Users']} />
        <Legend />
      </PieChart>
    </ResponsiveContainer>
  );
};

// Department Bandwidth Usage Pie Chart
const DepartmentBandwidthPieChart: React.FC<{ data: DepartmentPieData[] }> = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percentage }) => `${name}: ${percentage}%`}
          outerRadius={130}
          fill="#8884d8"
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip formatter={(value: any) => [formatBytes(value * 1024 * 1024 * 1024), 'Bandwidth']} />
        <Legend />
      </PieChart>
    </ResponsiveContainer>
  );
};

// Hourly Usage Pattern Chart
const HourlyUsageChart: React.FC<{ data: HourlyUsageData[] }> = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="hourLabel" />
        <YAxis />
        <Tooltip />
        <Legend />
        <Bar dataKey="usage" fill="#3b82f6" name="Bandwidth (MB)" />
        <Bar dataKey="users" fill="#10b981" name="Active Users" />
      </BarChart>
    </ResponsiveContainer>
  );
};

// User Trends Chart
const UserTrendsChart: React.FC<{ data: UserTrendData[] }> = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <AreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <defs>
          <linearGradient id="colorNew" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
            <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
          </linearGradient>
          <linearGradient id="colorReturning" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
            <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
          </linearGradient>
        </defs>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="date" tickFormatter={(value) => new Date(value).toLocaleDateString()} />
        <YAxis />
        <Tooltip labelFormatter={(value) => new Date(value).toLocaleDateString()} />
        <Legend />
        <Area
          type="monotone"
          dataKey="newUsers"
          stackId="1"
          stroke="#3b82f6"
          fill="url(#colorNew)"
          name="New Users"
        />
        <Area
          type="monotone"
          dataKey="returningUsers"
          stackId="1"
          stroke="#10b981"
          fill="url(#colorReturning)"
          name="Returning Users"
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};

// Session Duration Distribution Chart
const SessionDurationChart: React.FC<{ data: SessionDurationData[] }> = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={data} layout="horizontal" margin={{ top: 20, right: 30, left: 40, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis type="number" />
        <YAxis type="category" dataKey="range" />
        <Tooltip formatter={(value: any) => [value, 'Sessions']} />
        <Bar dataKey="count" fill="#8b5cf6" />
      </BarChart>
    </ResponsiveContainer>
  );
};

// Top Users Radial Chart
const TopUsersRadialChart: React.FC<{ data: TopUsersData[] }> = ({ data }) => {
  const processedData = data.slice(0, 10).map((user, index) => ({
    ...user,
    fill: user.isStaff ? '#3b82f6' : '#10b981',
    angle: (user.usage / Math.max(...data.map(d => d.usage))) * 360
  }));

  return (
    <ResponsiveContainer width="100%" height={400}>
      <RadialBarChart cx="50%" cy="50%" innerRadius="20%" outerRadius="90%" data={processedData}>
        <RadialBar
          minAngle={15}
          label={{ position: 'insideStart', fill: '#fff', fontSize: 10 }}
          background
          clockWise
          dataKey="usage"
        />
        <Legend iconSize={10} layout="vertical" verticalAlign="bottom" />
        <Tooltip formatter={(value: any) => [formatBytes(value * 1024 * 1024), 'Usage']} />
      </RadialBarChart>
    </ResponsiveContainer>
  );
};

// Bandwidth vs Users Scatter Plot
const BandwidthUsersScatterChart: React.FC<{ data: DepartmentAnalytics[] }> = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <ScatterChart data={data} margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
        <CartesianGrid />
        <XAxis type="number" dataKey="totalUsers" name="Users" />
        <YAxis type="number" dataKey="totalBandwidth" name="Bandwidth (GB)" />
        <Tooltip cursor={{ strokeDasharray: '3 3' }} 
          formatter={(value: any, name: string) => [
            name === 'totalBandwidth' ? `${value} GB` : value,
            name === 'totalBandwidth' ? 'Bandwidth' : 'Users'
          ]}
        />
        <Scatter data={data} fill="#3b82f6" />
      </ScatterChart>
    </ResponsiveContainer>
  );
};

// Department Treemap Chart
const DepartmentTreemapChart: React.FC<{ data: DepartmentAnalytics[] }> = ({ data }) => {
  const processedData = data.map((dept, index) => ({
    name: dept.departmentName,
    size: dept.totalBandwidth,
    fill: DEPARTMENT_COLORS[index % DEPARTMENT_COLORS.length]
  }));

  return (
    <ResponsiveContainer width="100%" height={350}>
      <Treemap
        data={processedData}
        dataKey="size"
        ratio={4/3}
        stroke="#fff"
        fill="#8884d8"
        content={<CustomTreemapContent />}
      />
    </ResponsiveContainer>
  );
};

// Custom Treemap Content
const CustomTreemapContent = (props: any) => {
  const { root, depth, x, y, width, height, index, payload, colors, name } = props;
  
  return (
    <g>
      <rect
        x={x}
        y={y}
        width={width}
        height={height}
        style={{
          fill: depth < 2 ? colors[Math.floor((index / root.children.length) * 6)] : 'none',
          stroke: '#fff',
          strokeWidth: 2 / (depth + 1e-10),
          strokeOpacity: 1 / (depth + 1e-10),
        }}
      />
      {depth === 1 ? (
        <text x={x + width / 2} y={y + height / 2 + 7} textAnchor="middle" fill="#fff" fontSize={12}>
          {name}
        </text>
      ) : null}
      {depth === 1 ? (
        <text x={x + 4} y={y + 18} fill="#fff" fontSize={10} fillOpacity={0.9}>
          {`${formatBytes(payload.size * 1024 * 1024 * 1024)}`}
        </text>
      ) : null}
    </g>
  );
};

export default function AdvancedAnalyticsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const defaultDates = getDefaultDates();

  // State management
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<"overview" | "users" | "departments" | "nas">("overview");
  const [startDate, setStartDate] = useState(defaultDates.start);
  const [endDate, setEndDate] = useState(defaultDates.end);
  
  // Data states
  const [overviewStats, setOverviewStats] = useState<OverviewStats | null>(null);
  const [individualStats, setIndividualStats] = useState<DepartmentStats[]>([]);
  const [departments, setDepartments] = useState<PackageInfo[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const [nasDevices, setNasDevices] = useState<NAS[]>([]);
  const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);
  const [userDetails, setUserDetails] = useState<UserDetail[]>([]);
  const [departmentAnalytics, setDepartmentAnalytics] = useState<DepartmentAnalytics[]>([]);
  const [nasAnalytics, setNasAnalytics] = useState<NASAnalytics[]>([]);
  const [hourlyUsageData, setHourlyUsageData] = useState<HourlyUsageData[]>([]);
  const [departmentPieData, setDepartmentPieData] = useState<DepartmentPieData[]>([]);
  const [userTrendData, setUserTrendData] = useState<UserTrendData[]>([]);
  const [sessionDurationData, setSessionDurationData] = useState<SessionDurationData[]>([]);
  const [topUsersData, setTopUsersData] = useState<TopUsersData[]>([]);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch functions
  const fetchDepartmentStats = async () => {
    setLoading(true);
    try {
      const payload = {
        group: departments.map((d) => d.package_name),
        start: `${startDate} 00:00:00`,
        end: `${endDate} 23:59:59`
      };

      const res = await apiClient.post("/department/stats", payload);
      const rawData = res?.data || [];
      
      setIndividualStats(rawData);
      processAnalyticsData(rawData);
    } catch (err) {
      console.error("Error fetching department stats:", err);
      toast.error("Error fetching analytics data");
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const res = await apiClient.get("/package");
      setDepartments(res?.data || []);
    } catch (err) {
      console.error("Failed to fetch departments:", err);
    }
  };

  const fetchCustomers = async () => {
    try {
      const res = await apiClient.get("/customer");
      setCustomers(res?.data || []);
    } catch (err) {
      console.error("Failed to fetch customers:", err);
      setCustomers([]);
    }
  };

  const fetchNasDevices = async () => {
    try {
      const res = await apiClient.get("/nas");
      setNasDevices(res?.data || []);
    } catch (err) {
      console.error("Failed to fetch NAS devices:", err);
      setNasDevices([]);
    }
  };

  // Process raw data into analytics
  const processAnalyticsData = (rawData: DepartmentStats[]) => {
    const customerUsernames = new Set(customers.map(c => c.username));
    
    // Overview stats
    const uniqueUsers = new Set(rawData.map(d => d.username));
    const staffUsers = Array.from(uniqueUsers).filter(u => customerUsernames.has(u));
    const guestUsers = Array.from(uniqueUsers).filter(u => !customerUsernames.has(u));
    
    const totalBandwidth = rawData.reduce((sum, d) => 
      sum + (Number(d.acctinputoctets) + Number(d.acctoutputoctets)), 0) / (1024 * 1024 * 1024);
    
    const avgDuration = rawData.length > 0 
      ? rawData.reduce((sum, d) => {
          const start = new Date(d.acctstarttime).getTime();
          const stop = d.acctstoptime ? new Date(d.acctstoptime).getTime() : Date.now();
          return sum + (stop - start) / 1000;
        }, 0) / rawData.length
      : 0;

    const overviewData: OverviewStats = {
      totalActiveUsers: uniqueUsers.size,
      totalStaffUsers: staffUsers.length,
      totalGuestUsers: guestUsers.length,
      totalSessions: rawData.length,
      totalBandwidthGB: totalBandwidth,
      avgSessionDuration: avgDuration,
      peakHourUsage: findPeakHour(rawData),
      departmentCount: departments.length,
    };
    
    setOverviewStats(overviewData);

    // Process user details
    const userDetailsMap = new Map<string, UserDetail>();
    rawData.forEach(session => {
      const username = session.username;
      if (!userDetailsMap.has(username)) {
        const isStaff = customerUsernames.has(username);
        const customer = customers.find(c => c.username === username);
        const dept = departments.find(d => d.package_name === session.groupname);
        
        userDetailsMap.set(username, {
          ...session,
          sessionCount: 0,
          avgSessionDuration: 0,
          totalBandwidthMB: 0,
          firstSeen: session.acctstarttime,
          lastSeen: session.acctstarttime,
          isStaff,
          department: session.groupname
        });
      }
      
      const user = userDetailsMap.get(username)!;
      user.sessionCount++;
      user.totalBandwidthMB += (Number(session.acctinputoctets) + Number(session.acctoutputoctets)) / (1024 * 1024);
      
      if (new Date(session.acctstarttime) < new Date(user.firstSeen)) {
        user.firstSeen = session.acctstarttime;
      }
      if (new Date(session.acctstarttime) > new Date(user.lastSeen)) {
        user.lastSeen = session.acctstarttime;
      }
    });

    setUserDetails(Array.from(userDetailsMap.values()));

    // Process department analytics
    const deptAnalyticsMap = new Map<string, DepartmentAnalytics>();
    departments.forEach(dept => {
      const deptSessions = rawData.filter(d => d.groupname === dept.package_name);
      const deptUsers = new Set(deptSessions.map(d => d.username));
      const staffInDept = Array.from(deptUsers).filter(u => customerUsernames.has(u));
      const guestInDept = Array.from(deptUsers).filter(u => !customerUsernames.has(u));
      
      const staffBandwidth = deptSessions
        .filter(d => customerUsernames.has(d.username))
        .reduce((sum, d) => sum + (Number(d.acctinputoctets) + Number(d.acctoutputoctets)), 0) / (1024 * 1024 * 1024);
      
      const guestBandwidth = deptSessions
        .filter(d => !customerUsernames.has(d.username))
        .reduce((sum, d) => sum + (Number(d.acctinputoctets) + Number(d.acctoutputoctets)), 0) / (1024 * 1024 * 1024);

      deptAnalyticsMap.set(dept.package_name, {
        departmentName: dept.package_name,
        staffCount: staffInDept.length,
        guestCount: guestInDept.length,
        totalUsers: deptUsers.size,
        staffBandwidth: staffBandwidth,
        guestBandwidth: guestBandwidth,
        totalBandwidth: staffBandwidth + guestBandwidth,
        avgSessionDuration: deptSessions.length > 0 
          ? deptSessions.reduce((sum, d) => {
              const start = new Date(d.acctstarttime).getTime();
              const stop = d.acctstoptime ? new Date(d.acctstoptime).getTime() : Date.now();
              return sum + (stop - start) / 1000;
            }, 0) / deptSessions.length
          : 0,
        peakUsageHour: findPeakHour(deptSessions)
      });
    });

    setDepartmentAnalytics(Array.from(deptAnalyticsMap.values()));

    // Process NAS analytics
    const nasAnalyticsMap = new Map<string, NASAnalytics>();
    nasDevices.forEach(nas => {
      const nasSessions = rawData.filter(d => d.nasipaddress === nas.nasname);
      const activeSessions = nasSessions.filter(d => !d.acctstoptime).length;
      
      nasAnalyticsMap.set(nas.nasname, {
        nasName: nas.name,
        nasIP: nas.nasname,
        totalSessions: nasSessions.length,
        activeSessions: activeSessions,
        totalBandwidthGB: nasSessions.reduce((sum, d) => 
          sum + (Number(d.acctinputoctets) + Number(d.acctoutputoctets)), 0) / (1024 * 1024 * 1024),
        avgSessionDuration: nasSessions.length > 0 
          ? nasSessions.reduce((sum, d) => {
              const start = new Date(d.acctstarttime).getTime();
              const stop = d.acctstoptime ? new Date(d.acctstoptime).getTime() : Date.now();
              return sum + (stop - start) / 1000;
            }, 0) / nasSessions.length
          : 0,
        vendor: nas.vendor || 'Unknown',
        description: nas.description || 'No description'
      });
    });

    setNasAnalytics(Array.from(nasAnalyticsMap.values()));

    // Process time series data
    const timeSeriesMap = new Map<string, TimeSeriesData>();
    rawData.forEach(session => {
      const date = new Date(session.acctstarttime).toISOString().split('T')[0];
      if (!timeSeriesMap.has(date)) {
        timeSeriesMap.set(date, {
          timestamp: date,
          activeUsers: 0,
          bandwidthMB: 0,
          sessions: 0
        });
      }
      
      const dayData = timeSeriesMap.get(date)!;
      dayData.sessions++;
      dayData.bandwidthMB += (Number(session.acctinputoctets) + Number(session.acctoutputoctets)) / (1024 * 1024);
    });

    // Calculate unique active users per day
    const dailyUsers = new Map<string, Set<string>>();
    rawData.forEach(session => {
      const date = new Date(session.acctstarttime).toISOString().split('T')[0];
      if (!dailyUsers.has(date)) {
        dailyUsers.set(date, new Set());
      }
      dailyUsers.get(date)!.add(session.username);
    });

    Array.from(timeSeriesMap.keys()).forEach(date => {
      const data = timeSeriesMap.get(date)!;
      data.activeUsers = dailyUsers.get(date)?.size || 0;
    });

    setTimeSeriesData(Array.from(timeSeriesMap.values()).sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()));

    // Process hourly usage data
    const hourlyUsageMap = new Map<number, HourlyUsageData>();
    rawData.forEach(session => {
      const hour = new Date(session.acctstarttime).getHours();
      if (!hourlyUsageMap.has(hour)) {
        hourlyUsageMap.set(hour, {
          hour,
          hourLabel: `${hour.toString().padStart(2, '0')}:00`,
          usage: 0,
          users: 0,
          sessions: 0
        });
      }
      
      const hourData = hourlyUsageMap.get(hour)!;
      hourData.usage += (Number(session.acctinputoctets) + Number(session.acctoutputoctets)) / (1024 * 1024);
      hourData.sessions++;
    });

    // Calculate unique users per hour
    const hourlyUsersMap = new Map<number, Set<string>>();
    rawData.forEach(session => {
      const hour = new Date(session.acctstarttime).getHours();
      if (!hourlyUsersMap.has(hour)) {
        hourlyUsersMap.set(hour, new Set());
      }
      hourlyUsersMap.get(hour)!.add(session.username);
    });

    Array.from(hourlyUsageMap.keys()).forEach(hour => {
      const data = hourlyUsageMap.get(hour)!;
      data.users = hourlyUsersMap.get(hour)?.size || 0;
    });

    // Fill missing hours with zero values
    for (let hour = 0; hour < 24; hour++) {
      if (!hourlyUsageMap.has(hour)) {
        hourlyUsageMap.set(hour, {
          hour,
          hourLabel: `${hour.toString().padStart(2, '0')}:00`,
          usage: 0,
          users: 0,
          sessions: 0
        });
      }
    }

    setHourlyUsageData(Array.from(hourlyUsageMap.values()).sort((a, b) => a.hour - b.hour));

    // Process department pie data
    const totalBandwidth = Array.from(deptAnalyticsMap.values()).reduce((sum, dept) => sum + dept.totalBandwidth, 0);
    const deptPieData = Array.from(deptAnalyticsMap.values()).map((dept, index) => ({
      name: dept.departmentName,
      value: dept.totalBandwidth,
      percentage: totalBandwidth > 0 ? Math.round((dept.totalBandwidth / totalBandwidth) * 100) : 0,
      color: DEPARTMENT_COLORS[index % DEPARTMENT_COLORS.length]
    })).filter(dept => dept.value > 0);

    setDepartmentPieData(deptPieData);

    // Process user trend data
    const userTrendMap = new Map<string, UserTrendData>();
    const existingUsersBeforeRange = new Set<string>();
    
    // For simplicity, we'll process the data we have
    Array.from(timeSeriesMap.keys()).forEach(date => {
      const dayUsers = dailyUsers.get(date) || new Set();
      const newUsers = Array.from(dayUsers).filter(user => !existingUsersBeforeRange.has(user));
      const returningUsers = Array.from(dayUsers).filter(user => existingUsersBeforeRange.has(user));
      
      userTrendMap.set(date, {
        date,
        newUsers: newUsers.length,
        returningUsers: returningUsers.length,
        totalActiveUsers: dayUsers.size
      });

      // Add current day users to existing users set
      dayUsers.forEach(user => existingUsersBeforeRange.add(user));
    });

    setUserTrendData(Array.from(userTrendMap.values()).sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()));

    // Process session duration data
    const durationRanges = [
      { min: 0, max: 300, label: '0-5 min' },
      { min: 300, max: 900, label: '5-15 min' },
      { min: 900, max: 1800, label: '15-30 min' },
      { min: 1800, max: 3600, label: '30-60 min' },
      { min: 3600, max: 7200, label: '1-2 hours' },
      { min: 7200, max: Infinity, label: '2+ hours' }
    ];

    const durationCounts = durationRanges.map(range => ({
      range: range.label,
      count: 0,
      percentage: 0
    }));

    rawData.forEach(session => {
      const start = new Date(session.acctstarttime).getTime();
      const stop = session.acctstoptime ? new Date(session.acctstoptime).getTime() : Date.now();
      const durationSeconds = (stop - start) / 1000;

      const rangeIndex = durationRanges.findIndex(range => 
        durationSeconds >= range.min && durationSeconds < range.max);
      if (rangeIndex >= 0) {
        durationCounts[rangeIndex].count++;
      }
    });

    // Calculate percentages
    const totalSessions = rawData.length;
    durationCounts.forEach(duration => {
      duration.percentage = totalSessions > 0 ? Math.round((duration.count / totalSessions) * 100) : 0;
    });

    setSessionDurationData(durationCounts);

    // Process top users data
    const topUsers = Array.from(userDetailsMap.values())
      .sort((a, b) => b.totalBandwidthMB - a.totalBandwidthMB)
      .slice(0, 20)
      .map(user => ({
        username: user.username,
        usage: user.totalBandwidthMB,
        sessions: user.sessionCount,
        avgDuration: user.avgSessionDuration,
        isStaff: user.isStaff
      }));

    setTopUsersData(topUsers);
  };

  const findPeakHour = (sessions: DepartmentStats[]): string => {
    const hourlyUsage = new Map<number, number>();
    
    sessions.forEach(session => {
      const hour = new Date(session.acctstarttime).getHours();
      const bandwidth = (Number(session.acctinputoctets) + Number(session.acctoutputoctets));
      hourlyUsage.set(hour, (hourlyUsage.get(hour) || 0) + bandwidth);
    });

    let peakHour = 0;
    let maxUsage = 0;
    hourlyUsage.forEach((usage, hour) => {
      if (usage > maxUsage) {
        maxUsage = usage;
        peakHour = hour;
      }
    });

    return `${peakHour.toString().padStart(2, '0')}:00`;
  };

  // Effects
  useEffect(() => {
    fetchDepartments();
    fetchCustomers();
    fetchNasDevices();
  }, []);

  useEffect(() => {
    if (departments.length > 0) {
      fetchDepartmentStats();
    }
  }, [departments, customers, nasDevices, startDate, endDate]);

  // Get active tab from URL
  useEffect(() => {
    const tabParam = searchParams.get("tab");
    if (tabParam && ["overview", "users", "departments", "nas"].includes(tabParam)) {
      setActiveTab(tabParam as any);
    }
  }, [searchParams]);

  const handleTabChange = (tab: "overview" | "users" | "departments" | "nas") => {
    setActiveTab(tab);
    setCurrentPage(1);
    
    const params = new URLSearchParams(searchParams.toString());
    params.set("tab", tab);
    router.replace(`?${params.toString()}`);
  };

  // Filter and paginate data
  const getFilteredData = () => {
    let data: any[] = [];
    
    switch (activeTab) {
      case "users":
        data = userDetails.filter(user => 
          user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.department.toLowerCase().includes(searchQuery.toLowerCase())
        );
        break;
      case "departments":
        data = departmentAnalytics.filter(dept =>
          dept.departmentName.toLowerCase().includes(searchQuery.toLowerCase())
        );
        break;
      case "nas":
        data = nasAnalytics.filter(nas =>
          nas.nasName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          nas.nasIP.toLowerCase().includes(searchQuery.toLowerCase())
        );
        break;
      default:
        data = [];
    }
    
    return data;
  };

  const filteredData = getFilteredData();
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage, 
    currentPage * itemsPerPage
  );

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Advanced Analytics</h1>
          <p className="text-gray-600 mt-1">Comprehensive insights into your network usage and performance</p>
        </div>
        
        {/* Date Range Filter */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 mt-4 sm:mt-0">
          <span className="text-sm font-medium text-gray-700">Date Range:</span>
          <div className="flex items-center gap-2">
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            />
            <span className="text-gray-400">to</span>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            />
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      {overviewStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100">Total Active Users</p>
                <p className="text-3xl font-bold">{overviewStats.totalActiveUsers}</p>
                <p className="text-sm text-blue-100">
                  Staff: {overviewStats.totalStaffUsers} | Guest: {overviewStats.totalGuestUsers}
                </p>
              </div>
              <Users className="h-12 w-12 text-blue-200" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100">Total Sessions</p>
                <p className="text-3xl font-bold">{overviewStats.totalSessions.toLocaleString()}</p>
                <p className="text-sm text-green-100">
                  Avg Duration: {formatDuration(overviewStats.avgSessionDuration)}
                </p>
              </div>
              <Sessions className="h-12 w-12 text-green-200" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-lg text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100">Bandwidth Usage</p>
                <p className="text-3xl font-bold">{overviewStats.totalBandwidthGB.toFixed(1)} GB</p>
                <p className="text-sm text-purple-100">
                  Peak Hour: {overviewStats.peakHourUsage}
                </p>
              </div>
              <Network className="h-12 w-12 text-purple-200" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-6 rounded-lg text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100">Departments</p>
                <p className="text-3xl font-bold">{overviewStats.departmentCount}</p>
                <p className="text-sm text-orange-100">
                  NAS Devices: {nasDevices.length}
                </p>
              </div>
              <Network className="h-12 w-12 text-orange-200" />
            </div>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: "overview", label: "Overview", icon: Activity },
            { id: "users", label: "Users", icon: Users },
            { id: "departments", label: "Departments", icon: Network },
            { id: "nas", label: "NAS Devices", icon: Router }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id as any)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === "overview" && (
          <>
            {/* Time Series Chart */}
            <div className="bg-white p-6 rounded-lg shadow border">
              <h2 className="text-xl font-semibold mb-4">Usage Trends Over Time</h2>
              <OverviewChart data={timeSeriesData} />
            </div>

            {/* Charts Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* User Distribution */}
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-xl font-semibold mb-4">User Type Distribution</h2>
                <UserTypeDistributionChart 
                  staffCount={overviewStats?.totalStaffUsers || 0}
                  guestCount={overviewStats?.totalGuestUsers || 0}
                />
              </div>

              {/* Department Performance */}
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-xl font-semibold mb-4">Top Department Performance</h2>
                <DepartmentPerformanceChart data={departmentAnalytics.slice(0, 5)} />
              </div>
            </div>

            {/* Department Bandwidth Distribution & Hourly Usage Patterns */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-4">Department Bandwidth Distribution</h2>
                <DepartmentBandwidthPieChart data={departmentPieData} />
              </div>
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-4">Hourly Usage Patterns</h2>
                <HourlyUsageChart data={hourlyUsageData} />
              </div>
            </div>

            {/* User Trends & Session Duration */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-4">User Trends (New vs Returning)</h2>
                <UserTrendsChart data={userTrendData} />
              </div>
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-4">Session Duration Distribution</h2>
                <SessionDurationChart data={sessionDurationData} />
              </div>
            </div>

            {/* Top Users Radial & Bandwidth vs Users Correlation */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-4">Top Users by Bandwidth (Radial View)</h2>
                <TopUsersRadialChart data={topUsersData} />
              </div>
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-4">Department: Users vs Bandwidth Correlation</h2>
                <BandwidthUsersScatterChart data={departmentAnalytics} />
              </div>
            </div>

            {/* Department Treemap - Full Width */}
            <div className="bg-white p-6 rounded-lg shadow border mb-6">
              <h2 className="text-lg font-semibold mb-4">Department Usage Treemap (Size = Bandwidth)</h2>
              <DepartmentTreemapChart data={departmentAnalytics} />
            </div>
          </>
        )}

        {activeTab === "users" && (
          <div className="space-y-6">
            {/* User Analytics Summary Cards */}
            <div className="bg-white p-6 rounded-lg shadow border">
              <h2 className="text-xl font-semibold mb-4">User Analytics Summary</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {(userDetails.reduce((sum, user) => sum + user.totalBandwidthMB, 0) / 1024).toFixed(1)} GB
                  </div>
                  <div className="text-sm text-blue-600">Total Bandwidth Usage</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {individualStats.length.toLocaleString()}
                  </div>
                  <div className="text-sm text-green-600">Total Sessions</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {userDetails.length}
                  </div>
                  <div className="text-sm text-purple-600">Unique Users</div>
                </div>
              </div>
            </div>

            {/* User Visualization Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-4">Top Users (Radial Chart)</h2>
                <TopUsersRadialChart data={topUsersData} />
              </div>
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-4">User Activity by Hour</h2>
                <HourlyUsageChart data={hourlyUsageData} />
              </div>
            </div>

            {/* User Trends and Session Distribution */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-4">New vs Returning Users</h2>
                <UserTrendsChart data={userTrendData} />
              </div>
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-4">Session Duration Patterns</h2>
                <SessionDurationChart data={sessionDurationData} />
              </div>
            </div>

            {/* Users Data Table */}
            <div className="bg-white rounded-lg shadow border">
            {/* Search and Controls */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                  <h2 className="text-xl font-semibold">User Analytics</h2>
                  <p className="text-gray-600">Detailed analysis of individual user behavior</p>
                </div>
                
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                  <input
                    type="text"
                    placeholder="Search users..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      setCurrentPage(1);
                    }}
                    className="border border-gray-300 rounded-md px-3 py-2 text-sm w-full sm:w-64"
                  />
                  <select
                    value={itemsPerPage}
                    onChange={(e) => {
                      setItemsPerPage(Number(e.target.value));
                      setCurrentPage(1);
                    }}
                    className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                  >
                    <option value={15}>15 per page</option>
                    <option value={25}>25 per page</option>
                    <option value={50}>50 per page</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Users Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bandwidth</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Duration</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Seen</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedData.map((user: UserDetail, index) => (
                    <tr key={user.username} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{user.username}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.isStaff ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                        }`}>
                          {user.isStaff ? 'Staff' : 'Guest'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.department}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.sessionCount}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatBytes(user.totalBandwidthMB * 1024 * 1024)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDuration(user.avgSessionDuration)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(user.lastSeen).toLocaleDateString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, filteredData.length)} of {filteredData.length} results
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    onClick={() => setCurrentPage(p => Math.max(p - 1, 1))}
                    disabled={currentPage === 1}
                    className="rounded-full w-8 h-8 p-0"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm px-2">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    size="sm"
                    onClick={() => setCurrentPage(p => Math.min(p + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="rounded-full w-8 h-8 p-0"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === "departments" && (
          <div className="space-y-6">
            {/* Department Performance Chart */}
            <div className="bg-white p-6 rounded-lg shadow border">
              <h2 className="text-xl font-semibold mb-4">Department Performance Overview</h2>
              <DepartmentPerformanceChart data={departmentAnalytics} />
            </div>

            {/* Additional Department Visualizations */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-4">Bandwidth Distribution (Pie Chart)</h2>
                <DepartmentBandwidthPieChart data={departmentPieData} />
              </div>
              <div className="bg-white p-6 rounded-lg shadow border">
                <h2 className="text-lg font-semibold mb-4">Users vs Bandwidth Correlation</h2>
                <BandwidthUsersScatterChart data={departmentAnalytics} />
              </div>
            </div>

            {/* Department Treemap */}
            <div className="bg-white p-6 rounded-lg shadow border">
              <h2 className="text-lg font-semibold mb-4">Department Usage Treemap</h2>
              <DepartmentTreemapChart data={departmentAnalytics} />
            </div>

            {/* Department Details Table */}
            <div className="bg-white rounded-lg shadow border">
              <div className="p-6 border-b border-gray-200">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <div>
                    <h2 className="text-xl font-semibold">Department Analytics</h2>
                    <p className="text-gray-600">Comprehensive department-wise usage statistics</p>
                  </div>
                  
                  <input
                    type="text"
                    placeholder="Search departments..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      setCurrentPage(1);
                    }}
                    className="border border-gray-300 rounded-md px-3 py-2 text-sm w-full sm:w-64"
                  />
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staff Users</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guest Users</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Users</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staff Bandwidth</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guest Bandwidth</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Bandwidth</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Duration</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Peak Hour</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {paginatedData.map((dept: DepartmentAnalytics) => (
                      <tr key={dept.departmentName} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{dept.departmentName}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600 font-medium">
                          {dept.staffCount}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                          {dept.guestCount}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                          {dept.totalUsers}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                          {formatBytes(dept.staffBandwidth * 1024 * 1024 * 1024)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                          {formatBytes(dept.guestBandwidth * 1024 * 1024 * 1024)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-purple-600 font-medium">
                          {formatBytes(dept.totalBandwidth * 1024 * 1024 * 1024)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDuration(dept.avgSessionDuration)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {dept.peakUsageHour}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {activeTab === "nas" && (
          <div className="space-y-6">
            {/* NAS Performance Chart */}
            <div className="bg-white p-6 rounded-lg shadow border">
              <h2 className="text-xl font-semibold mb-4">NAS Device Performance</h2>
              <NASPerformanceChart data={nasAnalytics} />
            </div>

            {/* NAS Details Table */}
            <div className="bg-white rounded-lg shadow border">
              <div className="p-6 border-b border-gray-200">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <div>
                    <h2 className="text-xl font-semibold">NAS Device Analytics</h2>
                    <p className="text-gray-600">Network Access Server performance metrics</p>
                  </div>
                  
                  <input
                    type="text"
                    placeholder="Search NAS devices..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      setCurrentPage(1);
                    }}
                    className="border border-gray-300 rounded-md px-3 py-2 text-sm w-full sm:w-64"
                  />
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">NAS Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendor</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Sessions</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active Sessions</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bandwidth Usage</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Duration</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {paginatedData.map((nas: NASAnalytics) => (
                      <tr key={nas.nasIP} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{nas.nasName}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 font-mono">{nas.nasIP}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {nas.vendor}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {nas.totalSessions.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            {nas.activeSessions}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-purple-600 font-medium">
                          {formatBytes(nas.totalBandwidthGB * 1024 * 1024 * 1024)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDuration(nas.avgSessionDuration)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate">
                          {nas.description}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Loading Overlay */}
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="text-gray-700">Loading analytics data...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}