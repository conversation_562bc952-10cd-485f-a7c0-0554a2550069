import React from "react";
import { useEffect, useState } from "react";
import { GraphRange } from "@/types/interface-type";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

// interface GraphRange {
//   label: string;
//   value: string;
// }

interface CustomerGraphSectionProps {
  selectedGraphRange: string;
  setSelectedGraphRange: (range: string) => void;
  graphRanges: GraphRange[]; // Pass the array of ranges
}

export const CustomerGraphSection: React.FC<CustomerGraphSectionProps> = ({
  graphRanges,
}) => {
  const [availableDates, setAvailableDates] = useState();
  const [selectedDate, setSelectedDate] = useState<string | null>();
  const [filteredGraphData, setFilteredGraphData] = useState([]);
  useEffect(() => {
    const uniqueDates = Array.from(
      new Set(
        graphRanges.map((d) => new Date(d.timestamp).toLocaleDateString())
      )
    );
    const sortedDates = uniqueDates.sort(
      (a, b) => new Date(b).getTime() - new Date(a).getTime()
    );
    const latestDate = sortedDates[0];
    // Step 3: Filter graph data by latestDate
    // const filtered = graphRanges.filter(
    //   (d) => new Date(d.timestamp).toISOString().slice(0, 10) === latestDate
    // );

    setAvailableDates(sortedDates);
    setSelectedDate(latestDate); // ✅ set selected date to latest
    // setFilteredGraphData(filtered);
  }, [graphRanges]);
  useEffect(() => {
    if (!selectedDate || !graphRanges || graphRanges.length === 0) return;

    const filtered = graphRanges.filter(
      (d) => new Date(d.timestamp).toLocaleDateString() === selectedDate
    );

    setFilteredGraphData(filtered);
  }, [selectedDate, graphRanges]);

  return (
    <div className="bg-white shadow-md rounded-lg p-5 space-y-4">
      <h3 className="text-lg font-semibold">Usage Graph</h3>
      {/* Graph Range Selector Buttons */}
      <div className="flex flex-wrap gap-2 justify-center sm:justify-start">
        <select className="p-1.5 rounded-md"
          onChange={(e) => setSelectedDate(e.target.value)}
          value={selectedDate}
        >
          {!selectedDate && (
            <option value="" disabled>
              Select a date
            </option>
          )}
          {availableDates?.map((date) => (
            <option key={date} value={date}>
              {date}
            </option>
          ))}
        </select>
      </div>
      {/* Render the RandomLineGraph component, passing the selected range */}
      {/* <RandomLineGraph timeRange={selectedGraphRange} /> */}
      {filteredGraphData.length > 0 ? (
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={filteredGraphData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="timeLabel" />
            <YAxis unit=" MB" />
            <Tooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey="upload"
              stroke="#8884d8"
              name="Upload"
            />
            <Line
              type="monotone"
              dataKey="download"
              stroke="#82ca9d"
              name="Download"
            />
          </LineChart>
        </ResponsiveContainer>
      ) : (
        <div className="text-center">
          <span>No session data found</span>
        </div>
      )}
    </div>
  );
};

export default CustomerGraphSection;
